import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';

import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/view.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/chathistory_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_api_usage.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/image_utils.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/chat_message_widgets.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';

// TODO: Add image gallery widget for better image browsing experience
// TODO: Implement image favorites/bookmarking system
// TODO: Add image metadata display (size, format, generation time)
// TODO: Implement image sharing functionality
// TODO: Add batch processing for multiple images
// TODO: Add style transfer and artistic filters
// TODO: Implement intelligent upscaling and enhancement
// TODO: Add video content creation capabilities

// IMPROVEMENTS MADE:
// ✅ Direct OpenAI API integration for image generation, editing, and variations
// ✅ Style settings now properly impact the final prompt and image generation
// ✅ Enhanced prompt building with comprehensive style integration
// ✅ Modern DALL-E 3 features including HD quality and native style parameters
// ✅ Visual feedback showing active style settings to users
// ✅ Better error handling and logging for debugging
// ✅ Proper API usage tracking for all image operations
// ✅ API usage limit checking before each direct API call
// ✅ User-friendly error messages for different failure scenarios

/// Enhanced image generation page for creating AI-generated images
/// Provides comprehensive image generation, editing, and variation capabilities
class GenerateImagePage extends StatefulWidget {
  /// Page title displayed in the app bar
  final String title;

  /// Usage type for tracking and billing purposes
  final Usage type;

  /// Current user ID for authentication and tracking
  final String currentUserId;

  const GenerateImagePage({
    super.key,
    required this.title,
    required this.type,
    required this.currentUserId,
  });

  @override
  State<GenerateImagePage> createState() => _GenerateImagePageState();
}

/// Available image operations for enhanced functionality
enum ImageOperation {
  /// Edit an existing image with AI
  editImage,

  /// Create variations of an existing image
  createVariations,
}

/// Style categories for image generation
enum StyleCategory {
  photorealistic,
  artistic,
  technical,
  creative,
}

/// Lighting and mood options
enum LightingMood {
  dramatic,
  soft,
  neon,
  vintage,
  natural,
  cinematic,
  golden,
  blue,
}

/// Composition guides
enum CompositionGuide {
  ruleOfThirds,
  goldenRatio,
  centered,
  leadingLines,
  symmetrical,
  asymmetrical,
}

/// Subject-specific templates
enum SubjectTemplate {
  portrait,
  landscape,
  product,
  logo,
  architecture,
  food,
  fashion,
  abstract,
}

/// Advanced image generation settings
///
/// Immutable configuration class for image generation parameters.
/// Follows best practices with const constructor and copyWith method.
class ImageGenerationSettings {
  /// Style category for the image (photorealistic, artistic, etc.)
  final StyleCategory? styleCategory;

  /// Specific style within the selected category
  final String? selectedStyle;

  /// Lighting and mood settings for the image
  final LightingMood? lightingMood;

  /// Composition guide for image layout
  final CompositionGuide? compositionGuide;

  /// Subject template for focused generation
  final SubjectTemplate? subjectTemplate;

  /// Aspect ratio for the generated image
  final double aspectRatio;

  /// Image size in pixels
  final int imageSize;

  /// Whether to use AI-enhanced prompting
  final bool useAdvancedPrompting;

  /// Negative prompt to avoid certain elements
  final String negativePrompt;

  const ImageGenerationSettings({
    this.styleCategory,
    this.selectedStyle,
    this.lightingMood,
    this.compositionGuide,
    this.subjectTemplate,
    this.aspectRatio = 1.0,
    this.imageSize = 1024,
    this.useAdvancedPrompting = false,
    this.negativePrompt = '',
  });

  /// Creates a copy of this settings object with the given fields replaced
  /// with new values. Follows Flutter best practices for immutable objects.
  ImageGenerationSettings copyWith({
    StyleCategory? styleCategory,
    String? selectedStyle,
    LightingMood? lightingMood,
    CompositionGuide? compositionGuide,
    SubjectTemplate? subjectTemplate,
    double? aspectRatio,
    int? imageSize,
    bool? useAdvancedPrompting,
    String? negativePrompt,
  }) {
    return ImageGenerationSettings(
      styleCategory: styleCategory ?? this.styleCategory,
      selectedStyle: selectedStyle ?? this.selectedStyle,
      lightingMood: lightingMood ?? this.lightingMood,
      compositionGuide: compositionGuide ?? this.compositionGuide,
      subjectTemplate: subjectTemplate ?? this.subjectTemplate,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      imageSize: imageSize ?? this.imageSize,
      useAdvancedPrompting: useAdvancedPrompting ?? this.useAdvancedPrompting,
      negativePrompt: negativePrompt ?? this.negativePrompt,
    );
  }
}

/// Style presets and constants for enhanced image generation
///
/// Provides predefined styles, lighting descriptions, and prompt building utilities
/// for creating high-quality AI-generated images.
class ImageStylePresets {
  // Private constructor to prevent instantiation
  ImageStylePresets._();

  /// Predefined styles organized by category
  static const Map<StyleCategory, List<String>> stylesByCategory = {
    StyleCategory.photorealistic: [
      'Photography',
      'Portrait Photography',
      'Landscape Photography',
      'Street Photography',
      'Documentary Style',
      'Fashion Photography',
      'Product Photography',
    ],
    StyleCategory.artistic: [
      'Oil Painting',
      'Watercolor',
      'Digital Art',
      'Impressionism',
      'Surrealism',
      'Cubism',
      'Abstract Art',
      'Pop Art',
      'Minimalism',
    ],
    StyleCategory.technical: [
      'Technical Drawing',
      'Blueprint',
      'Architectural Rendering',
      'Scientific Illustration',
      'Infographic Style',
      'Diagram',
    ],
    StyleCategory.creative: [
      'Cartoon',
      'Anime',
      'Vector Art',
      'Pixel Art',
      'Graffiti',
      'Comic Book',
      'Fantasy Art',
      'Steampunk',
      'Cyberpunk',
      'Retro',
      'Vintage',
    ],
  };

  static const Map<LightingMood, String> lightingDescriptions = {
    LightingMood.dramatic: 'dramatic lighting, high contrast, deep shadows',
    LightingMood.soft: 'soft lighting, diffused light, gentle shadows',
    LightingMood.neon: 'neon lighting, vibrant colors, electric atmosphere',
    LightingMood.vintage: 'vintage lighting, warm tones, nostalgic mood',
    LightingMood.natural: 'natural lighting, daylight, realistic illumination',
    LightingMood.cinematic: 'cinematic lighting, film-like quality, professional',
    LightingMood.golden: 'golden hour lighting, warm glow, sunset ambiance',
    LightingMood.blue: 'blue hour lighting, cool tones, twilight atmosphere',
  };

  static const Map<CompositionGuide, String> compositionDescriptions = {
    CompositionGuide.ruleOfThirds: 'rule of thirds composition, balanced framing',
    CompositionGuide.goldenRatio: 'golden ratio composition, harmonious proportions',
    CompositionGuide.centered: 'centered composition, symmetrical balance',
    CompositionGuide.leadingLines: 'leading lines composition, directional flow',
    CompositionGuide.symmetrical: 'symmetrical composition, mirror balance',
    CompositionGuide.asymmetrical: 'asymmetrical composition, dynamic balance',
  };

  static const Map<SubjectTemplate, String> subjectPrompts = {
    SubjectTemplate.portrait: 'portrait, face focus, detailed features, professional headshot',
    SubjectTemplate.landscape: 'landscape, wide view, natural scenery, environmental',
    SubjectTemplate.product: 'product photography, clean background, commercial style',
    SubjectTemplate.logo: 'logo design, simple, memorable, brand identity',
    SubjectTemplate.architecture: 'architectural, building design, structural details',
    SubjectTemplate.food: 'food photography, appetizing, detailed textures',
    SubjectTemplate.fashion: 'fashion photography, stylish, trendy, clothing focus',
    SubjectTemplate.abstract: 'abstract art, conceptual, non-representational',
  };

  /// Builds an enhanced prompt by combining base prompt with style settings
  ///
  /// Takes a base prompt and applies various style enhancements based on the
  /// provided settings. Returns a comprehensive prompt for AI image generation.
  ///
  /// [basePrompt] The original user prompt
  /// [settings] Configuration settings for style enhancement
  ///
  /// Returns enhanced prompt string ready for AI generation
  static String buildEnhancedPrompt(String basePrompt, ImageGenerationSettings settings) {
    if (basePrompt.trim().isEmpty) {
      throw ArgumentError('Base prompt cannot be empty');
    }

    final List<String> promptParts = [basePrompt.trim()];

    // Add style if selected - prioritize specific style over category
    if (settings.selectedStyle != null && settings.selectedStyle!.isNotEmpty) {
      promptParts.add('in ${settings.selectedStyle!.toLowerCase()} style');
    } else if (settings.styleCategory != null) {
      // Add general category style if no specific style selected
      switch (settings.styleCategory!) {
        case StyleCategory.photorealistic:
          promptParts.add('photorealistic, realistic photography');
          break;
        case StyleCategory.artistic:
          promptParts.add('artistic, creative art style');
          break;
        case StyleCategory.technical:
          promptParts.add('technical illustration, precise details');
          break;
        case StyleCategory.creative:
          promptParts.add('creative, imaginative art style');
          break;
      }
    }

    // Add lighting mood description
    if (settings.lightingMood != null) {
      final lightingDesc = lightingDescriptions[settings.lightingMood!];
      if (lightingDesc != null) {
        promptParts.add(lightingDesc);
      }
    }

    // Add composition guide
    if (settings.compositionGuide != null) {
      final compositionDesc = compositionDescriptions[settings.compositionGuide!];
      if (compositionDesc != null) {
        promptParts.add(compositionDesc);
      }
    }

    // Add subject template
    if (settings.subjectTemplate != null) {
      final subjectDesc = subjectPrompts[settings.subjectTemplate!];
      if (subjectDesc != null) {
        promptParts.add(subjectDesc);
      }
    }

    // Add negative prompt handling
    if (settings.negativePrompt.isNotEmpty) {
      promptParts.add('avoiding: ${settings.negativePrompt}');
    }

    // Add quality enhancers for better results (only if advanced prompting is enabled)
    if (settings.useAdvancedPrompting) {
      promptParts.addAll(const [
        'high quality',
        'detailed',
        'professional',
        '8k resolution',
        'masterpiece',
      ]);
    }

    final enhancedPrompt = promptParts.join(', ');

    // Log the enhancement for debugging
    logger.d('Original prompt: $basePrompt');
    logger.d('Enhanced prompt: $enhancedPrompt');

    return enhancedPrompt;
  }
}

/// State class for the GenerateImagePage widget
///
/// Manages all state related to image generation, user input, and UI interactions.
/// Follows Flutter best practices for state management and resource disposal.
class _GenerateImagePageState extends State<GenerateImagePage> {
  // MARK: - Core State Variables

  /// Current user input text for image generation
  String _inputText = '';

  /// URL of the most recently generated image
  String _imageUrl = '';

  /// Timestamp of the last image generation request
  DateTime _requestDateTime = DateTime.fromMillisecondsSinceEpoch(0);

  // MARK: - Input Metrics

  /// Current word count in the input text
  int _wordCount = 0;

  /// Current character count in the input text
  int _letterCount = 0;

  // MARK: - Image Handling

  /// Selected source image for editing or variations
  File? _image;

  /// Mask image for selective editing (optional)
  File? _mask;

  /// Image picker instance for selecting images from gallery
  final ImagePicker _picker = ImagePicker();

  // MARK: - UI State

  /// List of chat message widgets to display
  List<Widget> _chatMessages = [];

  /// Currently selected image operation (edit, variations, etc.)
  ImageOperation? _selectedOperation;

  /// Whether an image generation request is in progress
  bool _isLoading = false;

  /// Whether to show the advanced settings panel
  bool _showAdvancedSettings = false;

  /// Whether to show the style presets panel
  bool _showStylePresets = false;

  // MARK: - Controllers and Focus Management

  /// Text controller for the input field
  final TextEditingController _textController = TextEditingController();

  /// Stream controller for chat messages updates
  final StreamController<List<Widget>> _chatStreamController =
      StreamController<List<Widget>>.broadcast();

  /// Focus node for better input field handling
  final FocusNode _inputFocusNode = FocusNode();

  // MARK: - Image Generation Settings

  /// Current image generation configuration
  ImageGenerationSettings _generationSettings = const ImageGenerationSettings();

  /// Currently selected style category
  StyleCategory? _selectedStyleCategory;

  /// Currently selected specific style
  String? _selectedStyle;

  /// Currently selected lighting mood
  LightingMood? _selectedLightingMood;

  /// Currently selected composition guide
  CompositionGuide? _selectedCompositionGuide;

  /// Currently selected subject template
  SubjectTemplate? _selectedSubjectTemplate;

  // MARK: - Computed Properties

  /// Get the usage type from widget for tracking and billing
  Usage get usageType => widget.type;

  // MARK: - Lifecycle Methods

  @override
  void initState() {
    super.initState();
    // Initialize chat stream with empty messages
    _chatStreamController.add(_chatMessages);
  }

  @override
  void dispose() {
    // Dispose of controllers and streams to prevent memory leaks
    _chatStreamController.close();
    _textController.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  // MARK: - Image Handling Methods

  /// Picks an image from the gallery for editing or variations
  ///
  /// Allows users to select an image from their device gallery and processes
  /// it for use in image editing or variation generation operations.
  Future<void> _pickImage() async {
    try {
      final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        final File? processedImage = await ImageUtils.processImage(
          File(pickedFile.path),
        );
        if (processedImage != null) {
          setState(() {
            _image = processedImage;
          });
          logger.d('Image selected and processed successfully');
        } else {
          _showSnackBar('Invalid image format. Please select a valid image.');
          logger.w('Image processing failed - invalid format');
        }
      } else {
        logger.d('No image selected by user');
      }
    } catch (e) {
      _showSnackBar('Error selecting image. Please try again.');
      logger.e('Error picking image: $e');
    }
  }

  /// Edits the selected image using AI with the provided prompt
  ///
  /// Requires an image to be selected first. Uses the current text input
  /// as the editing instruction for the AI. Now calls OpenAI API directly.
  Future<void> _editImage() async {
    if (_image == null) {
      _showSnackBar('Please upload an image first.');
      return;
    }

    if (_textController.text.trim().isEmpty) {
      _showSnackBar('Please describe how you want to edit the image.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Build enhanced prompt for editing
      String finalPrompt = ImageStylePresets.buildEnhancedPrompt(
        _textController.text.trim(),
        _generationSettings,
      );

      final editedImageUrl = await _editImageDirect(
        _image!,
        _mask,
        finalPrompt,
        widget.currentUserId,
      );

      if (mounted) {
        setState(() {
          _inputText = _textController.text.trim(); // Store original input
          _imageUrl = editedImageUrl;
          _isLoading = false;
          _requestDateTime = DateTime.now().toUtc();
          _updateChatMessages();
        });

        // Save API usage for tracking
        await Util_API_Usage.saveApiUsage(
          responseId: "",
          apiUrl: 'https://api.openai.com/v1/images/edits',
          usageType: UsageType.generateImage.toString(),
          questionContent: finalPrompt,
          responseMessage: editedImageUrl,
          currentUserId: widget.currentUserId,
          conversationId: widget.currentUserId,
          question: _textController.text.trim(),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show appropriate error message based on error type
        String errorMessage = 'Failed to edit image. Please try again.';
        if (e.toString().contains('Usage limit')) {
          errorMessage = 'Usage limit exceeded. Please try again later or upgrade your plan.';
        } else if (e.toString().contains('API key')) {
          errorMessage = 'API configuration error. Please contact support.';
        }

        _showSnackBar(errorMessage);
        logger.e('Error editing image: $e');
      }
    }
  }

  /// Creates variations of the selected image using AI
  ///
  /// Generates multiple variations of the uploaded image while maintaining
  /// the core visual elements and style. Now calls OpenAI API directly.
  Future<void> _createVariations() async {
    if (_image == null) {
      _showSnackBar('Please upload an image first.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final variations = await _createVariationsDirect(
        _image!,
        widget.currentUserId,
      );

      if (mounted) {
        setState(() {
          _inputText = 'Image variations'; // Store operation description
          _imageUrl = variations;
          _isLoading = false;
          _requestDateTime = DateTime.now().toUtc();
          _updateChatMessages();
        });

        // Save API usage for tracking
        await Util_API_Usage.saveApiUsage(
          responseId: "",
          apiUrl: 'https://api.openai.com/v1/images/variations',
          usageType: UsageType.generateImage.toString(),
          questionContent: "",
          responseMessage: variations,
          currentUserId: widget.currentUserId,
          conversationId: widget.currentUserId,
          question: "Image variations",
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show appropriate error message based on error type
        String errorMessage = 'Failed to create variations. Please try again.';
        if (e.toString().contains('Usage limit')) {
          errorMessage = 'Usage limit exceeded. Please try again later or upgrade your plan.';
        } else if (e.toString().contains('API key')) {
          errorMessage = 'API configuration error. Please contact support.';
        }

        _showSnackBar(errorMessage);
        logger.e('Error creating variations: $e');
      }
    }
  }

  // MARK: - Direct OpenAI Image Generation

  /// Generates an image directly using OpenAI API with enhanced settings
  ///
  /// Calls OpenAI's DALL-E API directly with proper style integration and
  /// modern features like quality settings and aspect ratio support.
  ///
  /// [prompt] The enhanced prompt for image generation
  /// [settings] Image generation settings including style and parameters
  /// [userId] Current user ID for tracking
  ///
  /// Returns the Firebase URL of the generated image
  Future<String> _generateImageDirect(
    String prompt,
    ImageGenerationSettings settings,
    String userId,
  ) async {
    // Check usage limit before making API call
    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception('Usage limit (daily, monthly, specific API\'s) exceeded');
    }

    final String openaiApiKey = dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment("OPENAI_API_KEY");

    if (openaiApiKey.isEmpty) {
      throw Exception('OpenAI API key not configured');
    }

    // Prepare headers
    final headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $openaiApiKey",
    };

    // Determine image size based on aspect ratio and settings
    String imageSize = _getImageSize(settings.aspectRatio, settings.imageSize);

    // Build request body with modern DALL-E 3 features
    final requestBody = {
      "model": "dall-e-3", // Use DALL-E 3 for better quality
      "prompt": prompt,
      "n": 1, // DALL-E 3 only supports n=1
      "size": imageSize,
      "quality": "hd", // Use HD quality for better results
      "style": _getOpenAIStyle(settings), // Map our style to OpenAI's style parameter
      "response_format": "b64_json",
      "user": userId,
    };

    logger.d('OpenAI request: ${json.encode(requestBody)}');

    try {
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/images/generations'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final b64ImageData = responseData['data'][0]['b64_json'];

        // Save to Firebase Storage and return URL
        String firebaseUrl = await Util.SaveBase64ToFirebaseStorage(
          b64ImageData,
          userId,
        );

        logger.d('Image generated successfully: $firebaseUrl');
        return firebaseUrl;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['error']?['message'] ?? 'Unknown error';
        throw Exception('OpenAI API error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      logger.e('Error generating image: $e');
      throw Exception('Failed to generate image: $e');
    }
  }

  /// Maps image generation settings to OpenAI's native style parameter
  String _getOpenAIStyle(ImageGenerationSettings settings) {
    // OpenAI DALL-E 3 supports "vivid" and "natural" styles
    if (settings.styleCategory == StyleCategory.photorealistic ||
        settings.selectedStyle?.toLowerCase().contains('photography') == true) {
      return "natural";
    }
    return "vivid"; // Default to vivid for artistic and creative styles
  }

  /// Determines the appropriate image size based on aspect ratio and settings
  String _getImageSize(double aspectRatio, int imageSize) {
    // DALL-E 3 supports: 1024x1024, 1024x1792, 1792x1024
    if (aspectRatio > 1.2) {
      return "1792x1024"; // Landscape
    } else if (aspectRatio < 0.8) {
      return "1024x1792"; // Portrait
    } else {
      return "1024x1024"; // Square
    }
  }

  /// Edits an image directly using OpenAI API
  ///
  /// Calls OpenAI's image edit endpoint with the provided image, mask, and prompt.
  ///
  /// [image] The source image to edit
  /// [mask] Optional mask image for selective editing
  /// [prompt] The editing instruction
  /// [userId] Current user ID for tracking
  ///
  /// Returns the Firebase URL of the edited image
  Future<String> _editImageDirect(
    File image,
    File? mask,
    String prompt,
    String userId,
  ) async {
    // Check usage limit before making API call
    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception('Usage limit (daily, monthly, specific API\'s) exceeded');
    }

    final String openaiApiKey = dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment("OPENAI_API_KEY");

    if (openaiApiKey.isEmpty) {
      throw Exception('OpenAI API key not configured');
    }

    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.openai.com/v1/images/edits'),
      );

      request.headers['Authorization'] = 'Bearer $openaiApiKey';

      // Add form fields
      request.fields['prompt'] = prompt;
      request.fields['n'] = '1';
      request.fields['size'] = '1024x1024';
      request.fields['response_format'] = 'b64_json';
      request.fields['user'] = userId;

      // Add image file
      final imageBytes = await image.readAsBytes();
      request.files.add(http.MultipartFile.fromBytes(
        'image',
        imageBytes,
        filename: 'image.png',
      ));

      // Add mask file if provided
      if (mask != null) {
        final maskBytes = await mask.readAsBytes();
        request.files.add(http.MultipartFile.fromBytes(
          'mask',
          maskBytes,
          filename: 'mask.png',
        ));
      }

      final response = await http.Response.fromStream(await request.send());

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final b64ImageData = responseData['data'][0]['b64_json'];

        // Save to Firebase Storage and return URL
        String firebaseUrl = await Util.SaveBase64ToFirebaseStorage(
          b64ImageData,
          userId,
        );

        logger.d('Image edited successfully: $firebaseUrl');
        return firebaseUrl;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['error']?['message'] ?? 'Unknown error';
        throw Exception('OpenAI API error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      logger.e('Error editing image: $e');
      throw Exception('Failed to edit image: $e');
    }
  }

  /// Creates variations of an image directly using OpenAI API
  ///
  /// Calls OpenAI's image variations endpoint with the provided image.
  ///
  /// [image] The source image to create variations from
  /// [userId] Current user ID for tracking
  ///
  /// Returns the Firebase URL of the variation image
  Future<String> _createVariationsDirect(
    File image,
    String userId,
  ) async {
    // Check usage limit before making API call
    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception('Usage limit (daily, monthly, specific API\'s) exceeded');
    }

    final String openaiApiKey = dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment("OPENAI_API_KEY");

    if (openaiApiKey.isEmpty) {
      throw Exception('OpenAI API key not configured');
    }

    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.openai.com/v1/images/variations'),
      );

      request.headers['Authorization'] = 'Bearer $openaiApiKey';

      // Add form fields
      request.fields['n'] = '1';
      request.fields['size'] = '1024x1024';
      request.fields['response_format'] = 'b64_json';
      request.fields['user'] = userId;

      // Add image file
      final imageBytes = await image.readAsBytes();
      request.files.add(http.MultipartFile.fromBytes(
        'image',
        imageBytes,
        filename: 'image.png',
      ));

      final response = await http.Response.fromStream(await request.send());

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final b64ImageData = responseData['data'][0]['b64_json'];

        // Save to Firebase Storage and return URL
        String firebaseUrl = await Util.SaveBase64ToFirebaseStorage(
          b64ImageData,
          userId,
        );

        logger.d('Image variation created successfully: $firebaseUrl');
        return firebaseUrl;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['error']?['message'] ?? 'Unknown error';
        throw Exception('OpenAI API error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      logger.e('Error creating image variation: $e');
      throw Exception('Failed to create image variation: $e');
    }
  }

  // MARK: - Message Handling Methods

  /// Sends a message to generate a new image using AI
  ///
  /// Processes the user's text input, applies style enhancements,
  /// and calls OpenAI API directly to generate an image. Handles loading states and errors.
  ///
  /// [message] The user's prompt for image generation
  /// [usageType] The type of usage for tracking and billing
  Future<void> _sendMessage(String message, Usage usageType) async {
    if (message.trim().isEmpty) {
      _showSnackBar('Please enter a description for the image.');
      return;
    }

    // Show user feedback
    BotToast.showText(
      text: "Please be patient while image generation is in progress...",
      duration: const Duration(seconds: 3),
    );

    BotToast.showLoading();
    setState(() {
      _isLoading = true;
    });

    try {
      // Always build enhanced prompt with style settings
      String finalPrompt = ImageStylePresets.buildEnhancedPrompt(
        message.trim(),
        _generationSettings,
      );
      logger.d('Enhanced prompt: $finalPrompt');

      // Call OpenAI API directly for image generation
      final response = await _generateImageDirect(
        finalPrompt,
        _generationSettings,
        widget.currentUserId,
      );

      if (mounted) {
        setState(() {
          _inputText = message; // Store original input
          _imageUrl = response;
          _requestDateTime = DateTime.now().toUtc();
          _isLoading = false;
          _updateChatMessages();
        });

        // Save API usage for tracking
        await Util_API_Usage.saveApiUsage(
          responseId: "",
          apiUrl: 'https://api.openai.com/v1/images/generations',
          usageType: UsageType.generateImage.toString(),
          questionContent: finalPrompt,
          responseMessage: response,
          currentUserId: widget.currentUserId,
          conversationId: widget.currentUserId,
          question: message,
        );
      }
    } catch (e) {
      logger.e('Image generation failed: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // Show appropriate error message based on error type
        String errorMessage = 'Failed to generate image. Please try again.';
        if (e.toString().contains('Usage limit')) {
          errorMessage = 'Usage limit exceeded. Please try again later or upgrade your plan.';
        } else if (e.toString().contains('API key')) {
          errorMessage = 'API configuration error. Please contact support.';
        }

        _showSnackBar(errorMessage);
      }
    } finally {
      BotToast.closeAllLoading();
    }
  }

  // MARK: - Chat Message Management

  /// Creates chat message widgets for the current generation result
  ///
  /// Builds a complete chat message including header, body, image, and actions
  /// only if there's a valid generation result to display.
  ///
  /// Returns a list of widgets representing the chat message
  List<Widget> _createChatMessage() {
    return <Widget>[
      if (_requestDateTime != DateTime.fromMillisecondsSinceEpoch(0)) ...[
        Column(
          children: [
            ChatMessageHeader(requestDateTime: _requestDateTime),
            ChatMessageBody(inputText: _inputText),
            ChatMessageImage(imageUrl: _imageUrl),
            ChatMessageActions(
              imageUrl: _imageUrl,
              inputText: _inputText,
              currentUserId: widget.currentUserId,
            ),
          ],
        ),
      ],
    ];
  }

  /// Updates the chat messages list and notifies listeners
  ///
  /// Adds new chat messages to the existing list and updates the stream
  /// to trigger UI rebuilds. Called after successful image generation.
  void _updateChatMessages() {
    setState(() {
      _chatMessages.addAll(_createChatMessage());
    });
    _chatStreamController.sink.add(_chatMessages);
  }

  // MARK: - UI Building Methods

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: _buildAppBar(),
      body: _buildBody(context, theme, isDark),
    );
  }

  /// Builds the app bar with navigation actions
  ///
  /// Creates a consistent app bar with history and offline generation options
  /// following the app's design system.
  PreferredSizeWidget _buildAppBar() {
    return AppBarStyles.primary(
      title: 'AI Image Generator',
      actions: [
        // Chat History Button
        IconButton(
          key: const Key("ChatHistoryButtonOnChatPage"),
          icon: const Icon(Icons.history, color: Colors.white),
          tooltip: 'Image Generation History',
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChatHistoryPage(usage: usageType),
              ),
            );
          },
        ),
        // Offline Generation (iOS/macOS only)
        if (!kIsWeb && (Platform.isIOS || Platform.isMacOS))
          IconButton(
            key: const Key("GenerateImageButtonOfflinePage"),
            icon: const Icon(Icons.offline_bolt_outlined, color: Colors.white),
            tooltip: 'Offline Image Generation',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const OfflineImageGenerationPage(),
                ),
              );
            },
          ),
      ],
    );
  }

  /// Builds the main body of the page
  ///
  /// Organizes the main content including chat area, settings panels,
  /// image thumbnails, and input controls.
  Widget _buildBody(BuildContext context, ThemeData theme, bool isDark) {
    return Column(
      children: <Widget>[
        // Chat area - shows messages or empty state
        Expanded(
          child: _buildChatArea(context, theme),
        ),

        // Style presets panel (when visible)
        if (_showStylePresets) _buildStylePresetsPanel(theme),

        // Advanced settings panel (when visible)
        if (_showAdvancedSettings) _buildAdvancedSettingsPanel(theme),

        // Image thumbnails and controls (when images are selected)
        if (_image != null || _mask != null) _buildEnhancedImageThumbnails(),

        // Input area with controls
        _buildEnhancedInputArea(context, theme, isDark),
      ],
    );
  }

  /// Builds the chat area showing messages or empty state
  Widget _buildChatArea(BuildContext context, ThemeData theme) {
    if (_chatMessages.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    return StreamBuilder<List<Widget>>(
      stream: _chatStreamController.stream,
      initialData: _chatMessages,
      builder: (BuildContext context, AsyncSnapshot<List<Widget>> snapshot) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: snapshot.data?.length ?? 0,
          reverse: true,
          itemBuilder: (BuildContext context, int index) {
            final messages = snapshot.data!;
            return messages[messages.length - 1 - index];
          },
        );
      },
    );
  }

  // MARK: - Utility Methods

  /// Shows a snackbar with the given message
  ///
  /// Provides user feedback for various operations and error states.
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  // MARK: - Empty State and UI Components

  /// Builds an enhanced empty state widget with helpful messaging and tips
  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Main welcome card
          CardStyles.outlined(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Animated icon with glow effect
                TweenAnimationBuilder<double>(
                  duration: const Duration(seconds: 2),
                  tween: Tween(begin: 0.5, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryGreen.withValues(
                                alpha: 0.3,
                              ),
                              blurRadius: 20 * value,
                              spreadRadius: 5 * value,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.auto_awesome,
                          size: 64,
                          color: AppTheme.primaryGreen,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                Text(
                  'AI Image Generator',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Transform your imagination into stunning visuals with AI-powered image generation.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.hintColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    _inputFocusNode.requestFocus();
                  },
                  icon: Icon(Icons.create, color: Colors.white),
                  label: Text(
                    'Start Creating',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Tips and examples section
          _buildTipsSection(theme),

          // Feature highlights
          _buildFeatureHighlights(theme),
        ],
      ),
    );
  }

  /// Builds helpful tips section for users
  Widget _buildTipsSection(ThemeData theme) {
    final tips = [
      'Use Style Presets: Choose from photorealistic, artistic, technical, or creative styles',
      'Try Advanced Settings: Add lighting moods, composition guides, and subject focus',
      'Enable Enhanced Prompting: Let AI automatically improve your prompts',
      'Be specific: "A red sports car on a mountain road at sunset"',
      'Combine elements: Style + lighting + composition for best results',
    ];

    return CardStyles.outlined(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: AppTheme.secondaryGreen),
              const SizedBox(width: 8),
              Text(
                'Pro Tips for Better Results',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.secondaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...tips.map(
            (tip) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: AppTheme.primaryGreen,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      tip,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds feature highlights section
  Widget _buildFeatureHighlights(ThemeData theme) {
    final features = [
      {
        'icon': Icons.palette,
        'title': 'Style Presets',
        'description': 'Professional style library with 30+ presets',
      },
      {
        'icon': Icons.tune,
        'title': 'Advanced Controls',
        'description': 'Lighting, composition, and subject templates',
      },
      {
        'icon': Icons.auto_awesome,
        'title': 'Enhanced Prompting',
        'description': 'AI-powered prompt optimization',
      },
      {
        'icon': Icons.edit,
        'title': 'Image Editing',
        'description': 'Upload and edit existing images with AI',
      },
      {
        'icon': Icons.auto_fix_high,
        'title': 'Variations',
        'description': 'Create multiple versions of your images',
      },
      {
        'icon': Icons.offline_bolt,
        'title': 'Offline Mode',
        'description': 'Generate images locally on supported devices',
      },
    ];

    return CardStyles.outlined(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Features',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          ...features.map(
            (feature) => Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.cardColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    feature['icon'] as IconData,
                    color: AppTheme.primaryGreen,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'] as String,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          feature['description'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds enhanced image thumbnails with better styling
  Widget _buildEnhancedImageThumbnails() {
    return CardStyles.outlined(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.image, color: AppTheme.primaryGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Selected Images',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(Icons.clear, size: 18),
                onPressed: () {
                  setState(() {
                    _image = null;
                    _mask = null;
                  });
                },
                tooltip: 'Clear images',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              if (_image != null) ...[
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _image!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Source Image',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (_image != null && _mask != null) const SizedBox(width: 16),
              if (_mask != null) ...[
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _mask!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Mask Image',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.secondaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the enhanced input area with modern styling
  Widget _buildEnhancedInputArea(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Enhanced: Style and settings controls
              _buildStyleAndSettingsControls(theme),

              // Image operation controls (when image is selected)
              if (_image != null) _buildImageOperationControls(theme),

              // Input Row
              Container(
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Image upload button
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      child: IconButton(
                        icon: Icon(
                          Icons.add_photo_alternate,
                          color: _image != null
                              ? AppTheme.secondaryGreen
                              : AppTheme.primaryGreen,
                        ),
                        tooltip: _image != null
                            ? 'Change image'
                            : 'Upload image for editing',
                        onPressed: _pickImage,
                      ),
                    ),

                    // Text input field
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        child: TextFormField(
                          maxLines: null,
                          minLines: 1,
                          focusNode: _inputFocusNode,
                          key: const Key("GenerateImageInputTextField"),
                          controller: _textController,
                          onChanged: (value) {
                            setState(() {
                              _wordCount = Util_Token.countTokens(value.trim());
                              _letterCount = value.trim().length;
                            });
                          },
                          onFieldSubmitted: (value) {
                            if (value.trim().isNotEmpty) {
                              _handleSendMessage();
                            }
                          },
                          decoration: InputDecoration(
                            hintText: _getInputHintText(),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            hintStyle: TextStyle(color: theme.hintColor),
                          ),
                          style: theme.textTheme.bodyLarge,
                        ),
                      ),
                    ),

                    // Send button or loading indicator
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: _isLoading
                          ? Container(
                              width: 40,
                              height: 40,
                              padding: const EdgeInsets.all(8),
                              child: LoadingStyles.primary(size: 24),
                            )
                          : IconButton(
                              key: const Key("GenerateImageSendRequestButton"),
                              icon: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _canSendMessage()
                                      ? AppTheme.primaryGreen
                                      : theme.disabledColor,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  _getActionIcon(),
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              tooltip: _getActionTooltip(),
                              onPressed: _canSendMessage()
                                  ? _handleSendMessage
                                  : null,
                            ),
                    ),
                  ],
                ),
              ),

              // Word and character count
              if (_wordCount > 0 || _letterCount > 0)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Words: $_wordCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        'Characters: $_letterCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ],
                  ),
                ),

              // Style preview hint
              if (_hasActiveStyleSettings() && _textController.text.trim().isNotEmpty)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: 16,
                        color: AppTheme.primaryGreen,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Style enhancements will be applied to your prompt',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.primaryGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds image operation controls when an image is selected
  Widget _buildImageOperationControls(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CardStyles.outlined(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.tune, color: AppTheme.primaryGreen, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Image Operations',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildOperationChip(
                    'Edit Image',
                    ImageOperation.editImage,
                    Icons.edit,
                    theme,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildOperationChip(
                    'Create Variations',
                    ImageOperation.createVariations,
                    Icons.auto_fix_high,
                    theme,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildOperationChip(
                    'Generate New',
                    null,
                    Icons.auto_awesome,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds operation selection chips
  Widget _buildOperationChip(
    String label,
    ImageOperation? operation,
    IconData icon,
    ThemeData theme,
  ) {
    final isSelected = _selectedOperation == operation;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedOperation = operation;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryGreen.withValues(alpha: 0.1)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryGreen : theme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? AppTheme.primaryGreen : theme.hintColor,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected ? AppTheme.primaryGreen : theme.hintColor,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Gets appropriate hint text based on current state
  String _getInputHintText() {
    if (_image != null) {
      switch (_selectedOperation) {
        case ImageOperation.editImage:
          return "Describe how you want to edit this image...";
        case ImageOperation.createVariations:
          return "Describe variations you want to create...";
        default:
          return "Describe the new image you want to create...";
      }
    }
    return "Describe the image you want to create...";
  }

  /// Gets appropriate action icon based on current state
  IconData _getActionIcon() {
    if (_image != null) {
      switch (_selectedOperation) {
        case ImageOperation.editImage:
          return Icons.edit;
        case ImageOperation.createVariations:
          return Icons.auto_fix_high;
        default:
          return Icons.auto_awesome;
      }
    }
    return Icons.auto_awesome;
  }

  /// Gets appropriate tooltip based on current state
  String _getActionTooltip() {
    if (_image != null) {
      switch (_selectedOperation) {
        case ImageOperation.editImage:
          return 'Edit image';
        case ImageOperation.createVariations:
          return 'Create variations';
        default:
          return 'Generate new image';
      }
    }
    return 'Generate image';
  }

  /// Checks if message can be sent
  bool _canSendMessage() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (_image != null && _selectedOperation != null) {
      return hasText || _selectedOperation == ImageOperation.createVariations;
    }
    return hasText;
  }

  /// Handles sending message with appropriate action
  void _handleSendMessage() {
    final message = _textController.text.trim();

    if (_image != null && _selectedOperation != null) {
      switch (_selectedOperation!) {
        case ImageOperation.editImage:
          _editImage();
          break;
        case ImageOperation.createVariations:
          _createVariations();
          break;
      }
    } else {
      if (message.isNotEmpty) {
        _sendMessage(message, usageType);
      }
    }

    _textController.clear();
    setState(() {
      _wordCount = 0;
      _letterCount = 0;
    });
  }

  /// Builds style and settings controls
  Widget _buildStyleAndSettingsControls(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Style presets button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _showStylePresets = !_showStylePresets;
                  if (_showStylePresets) _showAdvancedSettings = false;
                });
              },
              icon: Icon(Icons.palette, size: 18),
              label: Text(_getStyleButtonText()),
              style: ElevatedButton.styleFrom(
                backgroundColor: _showStylePresets
                    ? AppTheme.primaryGreen
                    : theme.cardColor,
                foregroundColor: _showStylePresets
                    ? Colors.white
                    : theme.textTheme.bodyMedium?.color,
                elevation: _showStylePresets ? 2 : 0,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: _showStylePresets
                        ? AppTheme.primaryGreen
                        : theme.dividerColor,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Advanced settings button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _showAdvancedSettings = !_showAdvancedSettings;
                  if (_showAdvancedSettings) _showStylePresets = false;
                });
              },
              icon: Icon(Icons.tune, size: 18),
              label: Text(_getAdvancedButtonText()),
              style: ElevatedButton.styleFrom(
                backgroundColor: _showAdvancedSettings
                    ? AppTheme.primaryGreen
                    : theme.cardColor,
                foregroundColor: _showAdvancedSettings
                    ? Colors.white
                    : theme.textTheme.bodyMedium?.color,
                elevation: _showAdvancedSettings ? 2 : 0,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: _showAdvancedSettings
                        ? AppTheme.primaryGreen
                        : theme.dividerColor,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Enhanced prompting toggle
          Container(
            decoration: BoxDecoration(
              color: _generationSettings.useAdvancedPrompting
                  ? AppTheme.primaryGreen.withValues(alpha: 0.1)
                  : theme.cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _generationSettings.useAdvancedPrompting
                    ? AppTheme.primaryGreen
                    : theme.dividerColor,
              ),
            ),
            child: IconButton(
              icon: Icon(
                Icons.auto_awesome,
                color: _generationSettings.useAdvancedPrompting
                    ? AppTheme.primaryGreen
                    : theme.hintColor,
              ),
              tooltip: _generationSettings.useAdvancedPrompting
                  ? 'Enhanced prompting enabled'
                  : 'Enable enhanced prompting',
              onPressed: () {
                setState(() {
                  _generationSettings = _generationSettings.copyWith(
                    useAdvancedPrompting: !_generationSettings.useAdvancedPrompting,
                  );
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Builds style presets panel
  Widget _buildStylePresetsPanel(ThemeData theme) {
    return CardStyles.outlined(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.palette, color: AppTheme.primaryGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Style Presets',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close, size: 18),
                onPressed: () {
                  setState(() {
                    _showStylePresets = false;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Style categories
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: StyleCategory.values.map((category) {
                final isSelected = _selectedStyleCategory == category;
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getCategoryDisplayName(category)),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedStyleCategory = selected ? category : null;
                        _selectedStyle = null; // Reset style selection
                        _generationSettings = _generationSettings.copyWith(
                          styleCategory: _selectedStyleCategory,
                          selectedStyle: null,
                        );
                      });
                    },
                    selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                    checkmarkColor: AppTheme.primaryGreen,
                  ),
                );
              }).toList(),
            ),
          ),
          if (_selectedStyleCategory != null) ...[
            const SizedBox(height: 12),
            // Styles for selected category
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: ImageStylePresets.stylesByCategory[_selectedStyleCategory!]!
                  .map((style) {
                final isSelected = _selectedStyle == style;
                return FilterChip(
                  label: Text(style),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStyle = selected ? style : null;
                      _generationSettings = _generationSettings.copyWith(
                        styleCategory: _selectedStyleCategory,
                        selectedStyle: _selectedStyle,
                      );
                    });
                  },
                  selectedColor: AppTheme.secondaryGreen.withValues(alpha: 0.2),
                  checkmarkColor: AppTheme.secondaryGreen,
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// Gets display name for style category
  String _getCategoryDisplayName(StyleCategory category) {
    switch (category) {
      case StyleCategory.photorealistic:
        return 'Photo';
      case StyleCategory.artistic:
        return 'Artistic';
      case StyleCategory.technical:
        return 'Technical';
      case StyleCategory.creative:
        return 'Creative';
    }
  }

  /// Gets text for style button showing current selection
  String _getStyleButtonText() {
    if (_selectedStyle != null && _selectedStyle!.isNotEmpty) {
      return _selectedStyle!;
    } else if (_selectedStyleCategory != null) {
      return _getCategoryDisplayName(_selectedStyleCategory!);
    } else {
      return 'Style Presets';
    }
  }

  /// Gets text for advanced button showing active settings count
  String _getAdvancedButtonText() {
    int activeSettings = 0;
    if (_selectedLightingMood != null) activeSettings++;
    if (_selectedCompositionGuide != null) activeSettings++;
    if (_selectedSubjectTemplate != null) activeSettings++;

    if (activeSettings > 0) {
      return 'Advanced ($activeSettings)';
    } else {
      return 'Advanced';
    }
  }

  /// Checks if there are any active style settings
  bool _hasActiveStyleSettings() {
    return _selectedStyleCategory != null ||
           _selectedStyle != null ||
           _selectedLightingMood != null ||
           _selectedCompositionGuide != null ||
           _selectedSubjectTemplate != null ||
           _generationSettings.useAdvancedPrompting;
  }

  /// Builds advanced settings panel
  Widget _buildAdvancedSettingsPanel(ThemeData theme) {
    return CardStyles.outlined(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.tune, color: AppTheme.primaryGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Advanced Settings',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close, size: 18),
                onPressed: () {
                  setState(() {
                    _showAdvancedSettings = false;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Lighting & Mood
          _buildAdvancedSettingSection(
            'Lighting & Mood',
            Icons.wb_sunny,
            LightingMood.values.map((mood) {
              return FilterChip(
                label: Text(_getLightingDisplayName(mood)),
                selected: _selectedLightingMood == mood,
                onSelected: (selected) {
                  setState(() {
                    _selectedLightingMood = selected ? mood : null;
                    _generationSettings = _generationSettings.copyWith(
                      lightingMood: _selectedLightingMood,
                    );
                  });
                },
                selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryGreen,
              );
            }).toList(),
            theme,
          ),

          const SizedBox(height: 16),

          // Composition Guide
          _buildAdvancedSettingSection(
            'Composition',
            Icons.grid_on,
            CompositionGuide.values.map((guide) {
              return FilterChip(
                label: Text(_getCompositionDisplayName(guide)),
                selected: _selectedCompositionGuide == guide,
                onSelected: (selected) {
                  setState(() {
                    _selectedCompositionGuide = selected ? guide : null;
                    _generationSettings = _generationSettings.copyWith(
                      compositionGuide: _selectedCompositionGuide,
                    );
                  });
                },
                selectedColor: AppTheme.secondaryGreen.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.secondaryGreen,
              );
            }).toList(),
            theme,
          ),

          const SizedBox(height: 16),

          // Subject Template
          _buildAdvancedSettingSection(
            'Subject Focus',
            Icons.center_focus_strong,
            SubjectTemplate.values.map((template) {
              return FilterChip(
                label: Text(_getSubjectDisplayName(template)),
                selected: _selectedSubjectTemplate == template,
                onSelected: (selected) {
                  setState(() {
                    _selectedSubjectTemplate = selected ? template : null;
                    _generationSettings = _generationSettings.copyWith(
                      subjectTemplate: _selectedSubjectTemplate,
                    );
                  });
                },
                selectedColor: AppTheme.primaryGreen.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryGreen,
              );
            }).toList(),
            theme,
          ),
        ],
      ),
    );
  }

  /// Builds a section for advanced settings
  Widget _buildAdvancedSettingSection(
    String title,
    IconData icon,
    List<Widget> chips,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppTheme.secondaryGreen, size: 16),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.secondaryGreen,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: chips,
        ),
      ],
    );
  }

  /// Gets display name for lighting mood
  String _getLightingDisplayName(LightingMood mood) {
    switch (mood) {
      case LightingMood.dramatic:
        return 'Dramatic';
      case LightingMood.soft:
        return 'Soft';
      case LightingMood.neon:
        return 'Neon';
      case LightingMood.vintage:
        return 'Vintage';
      case LightingMood.natural:
        return 'Natural';
      case LightingMood.cinematic:
        return 'Cinematic';
      case LightingMood.golden:
        return 'Golden Hour';
      case LightingMood.blue:
        return 'Blue Hour';
    }
  }

  /// Gets display name for composition guide
  String _getCompositionDisplayName(CompositionGuide guide) {
    switch (guide) {
      case CompositionGuide.ruleOfThirds:
        return 'Rule of Thirds';
      case CompositionGuide.goldenRatio:
        return 'Golden Ratio';
      case CompositionGuide.centered:
        return 'Centered';
      case CompositionGuide.leadingLines:
        return 'Leading Lines';
      case CompositionGuide.symmetrical:
        return 'Symmetrical';
      case CompositionGuide.asymmetrical:
        return 'Asymmetrical';
    }
  }

  /// Gets display name for subject template
  String _getSubjectDisplayName(SubjectTemplate template) {
    switch (template) {
      case SubjectTemplate.portrait:
        return 'Portrait';
      case SubjectTemplate.landscape:
        return 'Landscape';
      case SubjectTemplate.product:
        return 'Product';
      case SubjectTemplate.logo:
        return 'Logo';
      case SubjectTemplate.architecture:
        return 'Architecture';
      case SubjectTemplate.food:
        return 'Food';
      case SubjectTemplate.fashion:
        return 'Fashion';
      case SubjectTemplate.abstract:
        return 'Abstract';
    }
  }
}
